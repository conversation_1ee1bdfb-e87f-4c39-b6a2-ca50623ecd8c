import { DBOS } from '@dbos-inc/dbos-sdk';
import { ChatService } from './core/ChatService';
import { GeminiLLMService } from './services/GeminiLLMService';
import { LLMServiceFactory } from './services/LLMService';
import { WebInterface } from './interfaces/WebInterface';
import { StreamingChatService } from './streaming';
import { SessionCleanupService } from './core/SessionCleanupService';
import { ForaChat } from './operations';
import config, { validateConfig } from './config';
import WebSocket from 'ws';
import http from 'http';
// Import operations to ensure workflow queues are initialized before DBOS launch
import './operations';

export class ForaChatApp {
  private chatService: ChatService;
  private webInterface: WebInterface;
  private streamingService: StreamingChatService;
  private server: http.Server | null = null;
  private wss: WebSocket.Server | null = null;
  private isInitialized: boolean = false;

  constructor() {
    // Validate configuration
    validateConfig();

    // Register LLM providers
    LLMServiceFactory.register('gemini', () => new GeminiLLMService());

    // Initialize services
    const llmService = LLMServiceFactory.create('gemini');
    this.chatService = new ChatService(llmService);
    this.webInterface = new WebInterface(this.chatService);
    this.streamingService = new StreamingChatService(this.chatService);
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    // Configure DBOS
    DBOS.setConfig({
      name: "forachat",
      databaseUrl: config.database.url,
      userDbclient: "knex" as any
    });

    // Launch DBOS with the web interface
    await DBOS.launch({ expressApp: this.webInterface.getApp() });
    console.log("✅ DBOS Launched successfully");

    // Start session cleanup service (runs every hour)
    SessionCleanupService.startCleanupService(60);
    console.log("✅ Session cleanup service started");

    this.isInitialized = true;
  }

  async start(): Promise<void> {
    await this.initialize();

    const app = this.webInterface.getApp();

    // Create HTTP server
    this.server = http.createServer(app);

    // Create WebSocket server
    this.wss = new WebSocket.Server({ server: this.server });

    // Handle WebSocket connections
    this.wss.on('connection', (ws: WebSocket, req: http.IncomingMessage) => {
      this.handleWebSocketConnection(ws, req);
    });

    this.server.listen(config.port, () => {
      console.log(`🚀 ForaChat server is running on http://localhost:${config.port}`);
      console.log(`📊 Environment: ${config.environment}`);
      console.log(`🤖 LLM Provider: ${config.llm.model}`);
      console.log('\n📚 Available endpoints:');
      console.log('  GET  / - Web UI');
      console.log('  POST /chat - Send a message');
      console.log('  GET  /health - Health check');
      console.log('  POST /conversation/:id/message - Continue conversation');
      console.log('  GET  /conversation/:id - Get conversation history');
      console.log('  WebSocket - Real-time chat interface');
    });
  }

  getChatService(): ChatService {
    return this.chatService;
  }

  getWebInterface(): WebInterface {
    return this.webInterface;
  }

  getStreamingService(): StreamingChatService {
    return this.streamingService;
  }

  private async handleWebSocketConnection(ws: WebSocket, req: http.IncomingMessage): Promise<void> {
    try {
      // Parse cookies from WebSocket request
      const cookies = this.parseCookies(req.headers.cookie || '');
      let sessionId = cookies.forachat_session;
      let session = null;

      if (sessionId) {
        // Try to get existing session
        const handle = await DBOS.startWorkflow(ForaChat).getSession(sessionId);
        session = await handle.getResult();
      }

      if (!session) {
        // Create new session for WebSocket
        sessionId = this.generateSessionId();
        const userIdentifier = req.socket.remoteAddress || 'unknown';

        const sessionRequest = {
          userIdentifier: `web_${userIdentifier}`,
          channel: 'web' as const,
          metadata: {
            userAgent: req.headers['user-agent'],
            ip: req.socket.remoteAddress
          }
        };

        const handle = await DBOS.startWorkflow(ForaChat).createSession(sessionRequest);
        session = await handle.getResult();
        sessionId = session.id;
      } else {
        // Update session activity
        await DBOS.startWorkflow(ForaChat).updateSessionActivity(sessionId);
      }

      console.log(`🔗 WebSocket connection with session: ${sessionId}`);
      this.streamingService.createSession(sessionId, ws, session);
    } catch (error) {
      console.error(`Error handling WebSocket connection: ${(error as Error).message}`);
      ws.close(1011, 'Internal server error');
    }
  }

  private parseCookies(cookieHeader: string): Record<string, string> {
    const cookies: Record<string, string> = {};
    cookieHeader.split(';').forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      if (name && value) {
        cookies[name] = decodeURIComponent(value);
      }
    });
    return cookies;
  }

  private generateSessionId(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }

  async shutdown(): Promise<void> {
    console.log('🛑 Shutting down ForaChat...');

    // Stop session cleanup service
    SessionCleanupService.stopCleanupService();

    // Close WebSocket server
    if (this.wss) {
      this.wss.close();
    }

    // Close HTTP server
    if (this.server) {
      this.server.close();
    }

    process.exit(0);
  }
}

// Graceful shutdown handling
process.on('SIGINT', async () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
  process.exit(0);
});
