import request from 'supertest';
import { DBOS } from '@dbos-inc/dbos-sdk';
import { ForaChatApp } from '../src/ForaChatApp';
import { SessionService } from '../src/core/SessionService';
import { ConversationService } from '../src/core/ConversationService';

describe('Web Interface Session Handling', () => {
  let app: ForaChatApp;
  let server: any;

  beforeAll(async () => {
    // Initialize DBOS for testing
    DBOS.setConfig({
      name: "forachat-test-web",
      databaseUrl: process.env.DATABASE_URL || "postgresql://localhost:5432/forachat_test",
      userDbclient: "knex" as any
    });

    app = new ForaChatApp();
    await app.initialize();
    server = app.getWebInterface().getApp();
  });

  afterAll(async () => {
    // Clean up test data
    await DBOS.knexClient('forachat.sessions').delete();
    await DBOS.knexClient('forachat.messages').delete();
    await DBOS.knexClient('forachat.conversations').delete();
  });

  beforeEach(async () => {
    // Clean up before each test
    await DBOS.knexClient('forachat.sessions').delete();
    await DBOS.knexClient('forachat.messages').delete();
    await DBOS.knexClient('forachat.conversations').delete();
  });

  describe('Session API Endpoints', () => {
    test('POST /api/session should create a new session', async () => {
      const sessionRequest = {
        userIdentifier: 'api_test_user',
        channel: 'web',
        metadata: { test: 'data' }
      };

      const response = await request(server)
        .post('/api/session')
        .send(sessionRequest)
        .expect(200);

      expect(response.body.sessionId).toBeDefined();
      expect(response.body.channel).toBe('web');
      expect(response.body.createdAt).toBeDefined();
    });

    test('GET /api/session/:id should return session info', async () => {
      // Create a session first
      const session = await SessionService.createSession({
        userIdentifier: 'api_get_user',
        channel: 'web'
      });

      const response = await request(server)
        .get(`/api/session/${session.id}`)
        .expect(200);

      expect(response.body.sessionId).toBe(session.id);
      expect(response.body.channel).toBe('web');
    });

    test('GET /api/session/:id should return 404 for non-existent session', async () => {
      const response = await request(server)
        .get('/api/session/non-existent-id')
        .expect(404);

      expect(response.body.error).toBe('Session not found');
    });

    test('DELETE /api/session/:id should delete session', async () => {
      // Create a session first
      const session = await SessionService.createSession({
        userIdentifier: 'api_delete_user',
        channel: 'web'
      });

      await request(server)
        .delete(`/api/session/${session.id}`)
        .expect(200);

      // Verify session is deleted
      const deletedSession = await SessionService.getSession(session.id);
      expect(deletedSession).toBeNull();
    });

    test('PUT /api/session/:id/extend should extend session', async () => {
      // Create a session first
      const session = await SessionService.createSession({
        userIdentifier: 'api_extend_user',
        channel: 'web'
      });

      const originalExpiry = session.expires_at!;

      await request(server)
        .put(`/api/session/${session.id}/extend`)
        .send({ hours: 48 })
        .expect(200);

      // Verify session was extended
      const extendedSession = await SessionService.getSession(session.id);
      expect(extendedSession!.expires_at!.getTime()).toBeGreaterThan(originalExpiry.getTime());
    });

    test('GET /api/session/:id/conversation should return session conversation', async () => {
      // Create session and conversation
      const session = await SessionService.createSession({
        userIdentifier: 'api_conv_user',
        channel: 'web'
      });

      const conversation = await ConversationService.createConversation();
      await SessionService.updateSessionConversation(session.id, conversation.id);
      await ConversationService.addMessage('user', 'test message', conversation.id);

      const response = await request(server)
        .get(`/api/session/${session.id}/conversation`)
        .expect(200);

      expect(response.body.sessionId).toBe(session.id);
      expect(response.body.conversation.id).toBe(conversation.id);
      expect(response.body.messages).toHaveLength(1);
      expect(response.body.messages[0].text).toBe('test message');
    });

    test('GET /api/user/:identifier/sessions should return user sessions', async () => {
      const userIdentifier = 'multi_session_api_user';

      // Create multiple sessions for the user
      const webSession = await SessionService.createSession({
        userIdentifier,
        channel: 'web'
      });

      const replSession = await SessionService.createSession({
        userIdentifier,
        channel: 'repl'
      });

      // Add conversations to make them active
      const conversation1 = await ConversationService.createConversation();
      const conversation2 = await ConversationService.createConversation();

      await SessionService.updateSessionConversation(webSession.id, conversation1.id);
      await SessionService.updateSessionConversation(replSession.id, conversation2.id);

      const response = await request(server)
        .get(`/api/user/${userIdentifier}/sessions`)
        .expect(200);

      expect(response.body.userIdentifier).toBe(userIdentifier);
      expect(response.body.sessions).toHaveLength(2);
    });

    test('POST /api/sessions/cleanup should cleanup expired sessions', async () => {
      // Create an expired session
      await SessionService.createSession({
        userIdentifier: 'cleanup_api_user',
        channel: 'web',
        expiresAt: new Date(Date.now() - 1000)
      });

      const response = await request(server)
        .post('/api/sessions/cleanup')
        .expect(200);

      expect(response.body.deletedCount).toBe(1);
      expect(response.body.message).toContain('Cleaned up 1 expired sessions');
    });

    test('GET /api/sessions/stats should return cleanup statistics', async () => {
      // Create some test data
      await SessionService.createSession({
        userIdentifier: 'stats_api_user',
        channel: 'web'
      });

      const conversation = await ConversationService.createConversation();
      await ConversationService.addMessage('user', 'stats test message', conversation.id);

      const response = await request(server)
        .get('/api/sessions/stats')
        .expect(200);

      expect(response.body.totalSessions).toBeGreaterThan(0);
      expect(response.body.totalConversations).toBeGreaterThan(0);
      expect(response.body.totalMessages).toBeGreaterThan(0);
      expect(response.body.activeSessions).toBeDefined();
      expect(response.body.expiredSessions).toBeDefined();
    });

    test('POST /api/sessions/manual-cleanup should perform manual cleanup', async () => {
      // Create expired session
      await SessionService.createSession({
        userIdentifier: 'manual_cleanup_user',
        channel: 'web',
        expiresAt: new Date(Date.now() - 1000)
      });

      const response = await request(server)
        .post('/api/sessions/manual-cleanup')
        .send({
          cleanupExpiredSessions: true,
          cleanupOrphanedConversations: false,
          cleanupOldMessages: false,
          cleanupMessageQueue: false
        })
        .expect(200);

      expect(response.body.message).toBe('Manual cleanup completed');
      expect(response.body.expiredSessions).toBe(1);
    });
  });

  describe('Session Middleware', () => {
    test('should create session cookie on first request', async () => {
      const response = await request(server)
        .get('/session')
        .expect(200);

      // Check that a session cookie was set
      const cookies = response.headers['set-cookie'];
      expect(cookies).toBeDefined();
      
      const cookieArray = Array.isArray(cookies) ? cookies : [cookies];
      const sessionCookie = cookieArray?.find((cookie: string) =>
        cookie.startsWith('forachat_session=')
      );
      expect(sessionCookie).toBeDefined();
    });

    test('should restore session from cookie', async () => {
      // First request to create session
      const firstResponse = await request(server)
        .get('/session')
        .expect(200);

      const cookies = firstResponse.headers['set-cookie'];
      const cookieArray = Array.isArray(cookies) ? cookies : [cookies];
      const sessionCookie = cookieArray?.find((cookie: string) =>
        cookie.startsWith('forachat_session=')
      );

      expect(sessionCookie).toBeDefined();

      // Extract session ID from cookie
      const sessionId = sessionCookie!.split('=')[1].split(';')[0];

      // Second request with the same cookie should use the same session
      const secondResponse = await request(server)
        .get('/session')
        .set('Cookie', `forachat_session=${sessionId}`)
        .expect(200);

      expect(secondResponse.body.sessionId).toBe(sessionId);
    });

    test('should handle chat requests with session', async () => {
      const response = await request(server)
        .post('/chat')
        .send({ text: 'Hello, this is a test message' })
        .expect(200);

      expect(response.body.conversationId).toBeDefined();
      
      // Check that a session cookie was set
      const cookies = response.headers['set-cookie'];
      expect(cookies).toBeDefined();
    });

    test('should continue conversation with existing session', async () => {
      // First chat request
      const firstResponse = await request(server)
        .post('/chat')
        .send({ text: 'First message' })
        .expect(200);

      const cookies = firstResponse.headers['set-cookie'];
      const cookieArray = Array.isArray(cookies) ? cookies : [cookies];
      const sessionCookie = cookieArray?.find((cookie: string) =>
        cookie.startsWith('forachat_session=')
      );

      const conversationId = firstResponse.body.conversationId;

      // Second chat request with same session
      const secondResponse = await request(server)
        .post('/chat')
        .set('Cookie', sessionCookie!)
        .send({ text: 'Second message' })
        .expect(200);

      // Should use the same conversation
      expect(secondResponse.body.conversationId).toBe(conversationId);
    });
  });

  describe('Error Handling', () => {
    test('should handle invalid session creation request', async () => {
      const response = await request(server)
        .post('/api/session')
        .send({ invalidField: 'value' })
        .expect(400);

      expect(response.body.error).toContain('userIdentifier and channel are required');
    });

    test('should handle session not found gracefully', async () => {
      const response = await request(server)
        .get('/api/session/invalid-session-id')
        .expect(404);

      expect(response.body.error).toBe('Session not found');
    });

    test('should handle conversation not found for session', async () => {
      // Create session without conversation
      const session = await SessionService.createSession({
        userIdentifier: 'no_conv_user',
        channel: 'web'
      });

      const response = await request(server)
        .get(`/api/session/${session.id}/conversation`)
        .expect(404);

      expect(response.body.error).toBe('No conversation found for this session');
    });
  });
});
